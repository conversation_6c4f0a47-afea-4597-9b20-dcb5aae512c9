# Restaurant POS Dashboard

A modern, responsive restaurant point-of-sale dashboard with RTL/LTR localization support, built with vanilla HTML, CSS, and JavaScript.

## Features

### 🌐 Multilingual Support
- **English (LTR)** and **Arabic (RTL)** localization
- Automatic text direction switching
- Font optimization for both languages
- Persistent language preference

### 📱 Responsive Design
- **Mobile-first** approach
- Touch-friendly navigation
- Collapsible sidebar for mobile
- Optimized layouts for all screen sizes

### 🎨 Modern UI/UX
- Clean, professional design
- CSS Grid and Flexbox layouts
- Custom CSS properties for theming
- Smooth animations and transitions
- Accessibility-focused design

### ⚡ Performance Optimized
- Service Worker for offline functionality
- Lazy loading for images
- Debounced search functionality
- Efficient DOM manipulation
- PWA capabilities

### 🧭 Navigation Features
- Collapsible sidebar with sub-menus
- Breadcrumb navigation
- Keyboard shortcuts (Alt+M for sidebar, Alt+L for language)
- Active state management

### 📊 Dashboard Components
- Real-time statistics cards
- Sales overview charts (placeholder)
- Recent orders list
- Table occupancy status
- Pending orders tracking

## File Structure

```
restaurant-pos-dashboard/
├── index.html          # Main HTML structure
├── styles.css          # Complete CSS styling with RTL support
├── script.js           # JavaScript functionality and localization
├── sw.js              # Service Worker for PWA features
├── manifest.json      # PWA manifest file
└── README.md          # This file
```

## Quick Start

1. **Clone or download** the files to your web server directory
2. **Open** `index.html` in a modern web browser
3. **Test** the responsive design by resizing the browser window
4. **Switch languages** using the language dropdown in the navbar
5. **Try mobile view** by opening on a mobile device or using browser dev tools

## Browser Support

- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+
- ✅ Mobile browsers (iOS Safari, Chrome Mobile)

## Keyboard Shortcuts

- `Alt + M` - Toggle sidebar
- `Alt + L` - Switch language
- `Escape` - Close sidebar/dropdowns

## Customization

### Colors and Theming
Edit CSS custom properties in `styles.css`:
```css
:root {
  --primary-500: #3b82f6;  /* Primary brand color */
  --primary-600: #2563eb;  /* Primary hover color */
  /* ... other color variables */
}
```

### Adding New Languages
1. Add translations to the `translations` object in `script.js`
2. Add language option to the dropdown in `index.html`
3. Update font families if needed in CSS

### Menu Items
Modify the sidebar navigation in `index.html` and update corresponding translations in `script.js`.

## Performance Features

### Service Worker
- Caches static files for offline access
- Background sync for offline orders
- Push notification support
- Automatic cache management

### Optimization Techniques
- CSS/JS minification ready
- Image lazy loading
- Debounced search (300ms)
- Throttled resize events (250ms)
- Efficient event delegation

## Accessibility

- Semantic HTML structure
- ARIA labels and roles
- Keyboard navigation support
- Focus management
- High contrast mode support
- Screen reader friendly

## PWA Features

- Installable on mobile devices
- Offline functionality
- App shortcuts
- Custom splash screen
- Theme color integration

## Development Notes

### RTL Support Implementation
- CSS logical properties used where appropriate
- Direction-specific styling with `[dir="rtl"]` selectors
- Font family switching for Arabic text
- Icon and layout adjustments for RTL

### Performance Considerations
- Minimal external dependencies
- Efficient CSS selectors
- Optimized JavaScript execution
- Lazy loading implementation
- Service Worker caching strategy

## Browser Testing

Test the application in different scenarios:

1. **Desktop browsers** - Full functionality
2. **Mobile devices** - Touch navigation, responsive layout
3. **Offline mode** - Service Worker caching
4. **RTL languages** - Arabic text direction
5. **Keyboard navigation** - Accessibility features

## Future Enhancements

- Integration with real POS backend API
- Chart.js integration for data visualization
- Real-time order updates with WebSockets
- Print functionality for receipts
- Advanced reporting features
- Multi-restaurant support

## License

This project is open source and available under the MIT License.

## Support

For questions or issues, please refer to the code comments or create an issue in the project repository.
