/* ===== CSS CUSTOM PROPERTIES ===== */
:root {
  /* Colors */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  --success-500: #10b981;
  --warning-500: #f59e0b;
  --error-500: #ef4444;
  
  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-12: 3rem;
  --space-16: 4rem;
  
  /* Typography */
  --font-family-en: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-family-ar: 'Noto Sans Arabic', 'Inter', sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  
  /* Layout */
  --navbar-height: 4rem;
  --sidebar-width: 16rem;
  --sidebar-collapsed-width: 4rem;
  --border-radius: 0.5rem;
  --border-radius-lg: 0.75rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* ===== RESET & BASE STYLES ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-en);
  font-size: var(--font-size-base);
  line-height: 1.5;
  color: var(--gray-900);
  background-color: var(--gray-50);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* RTL Support */
[dir="rtl"] {
  font-family: var(--font-family-ar);
}

[dir="rtl"] .logo-text,
[dir="rtl"] .nav-link,
[dir="rtl"] .submenu-link {
  font-weight: 500;
}

/* ===== LOADING SCREEN ===== */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: opacity var(--transition-normal), visibility var(--transition-normal);
}

.loading-screen.hidden {
  opacity: 0;
  visibility: hidden;
}

.loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 2px solid var(--gray-200);
  border-top: 2px solid var(--primary-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-4);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== APP CONTAINER ===== */
.app-container {
  display: grid;
  grid-template-areas: 
    "navbar navbar"
    "sidebar main";
  grid-template-columns: var(--sidebar-width) 1fr;
  grid-template-rows: var(--navbar-height) 1fr;
  min-height: 100vh;
  transition: grid-template-columns var(--transition-normal);
}

.app-container.sidebar-collapsed {
  grid-template-columns: var(--sidebar-collapsed-width) 1fr;
}

/* ===== NAVBAR ===== */
.navbar {
  grid-area: navbar;
  background: white;
  border-bottom: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
  z-index: 1000;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 var(--space-6);
}

.navbar-brand {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.sidebar-toggle {
  display: none;
  flex-direction: column;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--border-radius);
  transition: background-color var(--transition-fast);
}

.sidebar-toggle:hover {
  background-color: var(--gray-100);
}

.hamburger-line {
  width: 1.25rem;
  height: 2px;
  background-color: var(--gray-600);
  margin: 2px 0;
  transition: var(--transition-fast);
  border-radius: 1px;
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  color: var(--primary-600);
}

.logo-text {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--gray-900);
}

/* Search Bar */
.navbar-search {
  flex: 1;
  max-width: 28rem;
  margin: 0 var(--space-8);
}

.search-container {
  position: relative;
  width: 100%;
}

.search-icon {
  position: absolute;
  left: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-400);
  pointer-events: none;
}

[dir="rtl"] .search-icon {
  left: auto;
  right: var(--space-3);
}

.search-input {
  width: 100%;
  height: 2.5rem;
  padding: 0 var(--space-3) 0 2.5rem;
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  background: white;
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

[dir="rtl"] .search-input {
  padding: 0 2.5rem 0 var(--space-3);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

/* Navbar Controls */
.navbar-controls {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.language-switcher {
  position: relative;
}

.lang-btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background: none;
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: var(--font-size-sm);
  color: var(--gray-700);
  transition: all var(--transition-fast);
}

.lang-btn:hover {
  background-color: var(--gray-50);
  border-color: var(--gray-400);
}

.lang-dropdown {
  position: absolute;
  top: calc(100% + var(--space-2));
  right: 0;
  background: white;
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  min-width: 8rem;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-0.5rem);
  transition: all var(--transition-fast);
  z-index: 1001;
}

[dir="rtl"] .lang-dropdown {
  right: auto;
  left: 0;
}

.lang-dropdown.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.lang-option {
  display: block;
  width: 100%;
  padding: var(--space-3);
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  font-size: var(--font-size-sm);
  color: var(--gray-700);
  transition: background-color var(--transition-fast);
}

[dir="rtl"] .lang-option {
  text-align: right;
}

.lang-option:hover {
  background-color: var(--gray-50);
}

.notification-btn {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background: none;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  color: var(--gray-600);
  transition: all var(--transition-fast);
}

.notification-btn:hover {
  background-color: var(--gray-100);
  color: var(--gray-900);
}

.notification-badge {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  width: 1.25rem;
  height: 1.25rem;
  background: var(--error-500);
  color: white;
  font-size: var(--font-size-xs);
  font-weight: 600;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-profile {
  position: relative;
}

.profile-btn {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-2);
  background: none;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.profile-btn:hover {
  background-color: var(--gray-100);
}

.profile-avatar {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
}

.profile-name {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--gray-700);
}

/* ===== SIDEBAR ===== */
.sidebar {
  grid-area: sidebar;
  background: white;
  border-right: 1px solid var(--gray-200);
  overflow-y: auto;
  transition: transform var(--transition-normal);
}

[dir="rtl"] .sidebar {
  border-right: none;
  border-left: 1px solid var(--gray-200);
}

.sidebar-content {
  padding: var(--space-6) 0;
}

.sidebar-nav {
  padding: 0 var(--space-4);
}

.nav-list {
  list-style: none;
}

.nav-item {
  margin-bottom: var(--space-2);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  width: 100%;
  padding: var(--space-3) var(--space-4);
  color: var(--gray-700);
  text-decoration: none;
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  font-weight: 500;
  transition: all var(--transition-fast);
  background: none;
  border: none;
  cursor: pointer;
  text-align: left;
}

[dir="rtl"] .nav-link {
  text-align: right;
}

.nav-link:hover {
  background-color: var(--gray-100);
  color: var(--gray-900);
}

.nav-link.active {
  background-color: var(--primary-50);
  color: var(--primary-700);
}

.nav-icon {
  flex-shrink: 0;
  color: currentColor;
}

.submenu-toggle {
  justify-content: space-between;
}

.submenu-arrow {
  flex-shrink: 0;
  transition: transform var(--transition-fast);
}

[dir="rtl"] .submenu-arrow {
  transform: rotate(180deg);
}

.nav-item.expanded .submenu-arrow {
  transform: rotate(90deg);
}

[dir="rtl"] .nav-item.expanded .submenu-arrow {
  transform: rotate(270deg);
}

.submenu {
  list-style: none;
  max-height: 0;
  overflow: hidden;
  transition: max-height var(--transition-normal);
  margin-top: var(--space-2);
}

.nav-item.expanded .submenu {
  max-height: 12rem;
}

.submenu-link {
  display: block;
  padding: var(--space-2) var(--space-4);
  margin-left: 2.75rem;
  color: var(--gray-600);
  text-decoration: none;
  font-size: var(--font-size-sm);
  border-radius: var(--border-radius);
  transition: all var(--transition-fast);
}

[dir="rtl"] .submenu-link {
  margin-left: 0;
  margin-right: 2.75rem;
}

.submenu-link:hover {
  background-color: var(--gray-100);
  color: var(--gray-900);
}

/* ===== MAIN CONTENT ===== */
.main-content {
  grid-area: main;
  overflow-y: auto;
  background-color: var(--gray-50);
}

.content-wrapper {
  padding: var(--space-6);
  max-width: 100%;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-8);
  flex-wrap: wrap;
  gap: var(--space-4);
}

.page-title h1 {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--space-1);
}

.page-subtitle {
  color: var(--gray-600);
  font-size: var(--font-size-base);
}

.page-actions {
  display: flex;
  gap: var(--space-3);
}

/* ===== BUTTONS ===== */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  font-size: var(--font-size-sm);
  font-weight: 500;
  border-radius: var(--border-radius);
  border: 1px solid transparent;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  white-space: nowrap;
}

.btn-primary {
  background-color: var(--primary-600);
  color: white;
  border-color: var(--primary-600);
}

.btn-primary:hover {
  background-color: var(--primary-700);
  border-color: var(--primary-700);
}

.btn-secondary {
  background-color: white;
  color: var(--gray-700);
  border-color: var(--gray-300);
}

.btn-secondary:hover {
  background-color: var(--gray-50);
  border-color: var(--gray-400);
}

/* ===== DASHBOARD CONTENT ===== */
.dashboard-content {
  display: grid;
  gap: var(--space-6);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-6);
}

.stat-card {
  background: white;
  padding: var(--space-6);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
}

.stat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-4);
}

.stat-title {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--gray-600);
}

.stat-icon {
  width: 2rem;
  height: 2rem;
  padding: var(--space-2);
  border-radius: var(--border-radius);
  background-color: var(--primary-50);
  color: var(--primary-600);
}

.stat-value {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--space-1);
}

.stat-change {
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.stat-change.positive {
  color: var(--success-500);
}

.stat-change.negative {
  color: var(--error-500);
}

/* ===== MOBILE OVERLAY ===== */
.mobile-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.mobile-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .navbar-search {
    max-width: 20rem;
    margin: 0 var(--space-4);
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

@media (max-width: 768px) {
  :root {
    --navbar-height: 3.5rem;
    --sidebar-width: 16rem;
  }

  .app-container {
    grid-template-areas:
      "navbar"
      "main";
    grid-template-columns: 1fr;
    grid-template-rows: var(--navbar-height) 1fr;
  }

  .sidebar {
    position: fixed;
    top: var(--navbar-height);
    left: 0;
    width: var(--sidebar-width);
    height: calc(100vh - var(--navbar-height));
    z-index: 1000;
    transform: translateX(-100%);
    box-shadow: var(--shadow-lg);
  }

  [dir="rtl"] .sidebar {
    left: auto;
    right: 0;
    transform: translateX(100%);
  }

  .sidebar.show {
    transform: translateX(0);
  }

  .sidebar-toggle {
    display: flex;
  }

  .navbar-content {
    padding: 0 var(--space-4);
  }

  .navbar-search {
    display: none;
  }

  .content-wrapper {
    padding: var(--space-4);
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-4);
  }

  .page-title h1 {
    font-size: var(--font-size-2xl);
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .mobile-overlay {
    display: block;
  }
}

@media (max-width: 480px) {
  .navbar-controls {
    gap: var(--space-2);
  }

  .profile-name {
    display: none;
  }

  .lang-btn {
    padding: var(--space-2);
  }

  .lang-text {
    font-size: var(--font-size-xs);
  }

  .content-wrapper {
    padding: var(--space-3);
  }

  .page-title h1 {
    font-size: var(--font-size-xl);
  }

  .btn {
    padding: var(--space-2) var(--space-3);
    font-size: var(--font-size-xs);
  }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus styles for keyboard navigation */
.nav-link:focus,
.submenu-link:focus,
.btn:focus,
.search-input:focus,
.lang-btn:focus,
.notification-btn:focus,
.profile-btn:focus,
.sidebar-toggle:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --gray-100: #e0e0e0;
    --gray-200: #c0c0c0;
    --gray-300: #a0a0a0;
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .navbar,
  .sidebar,
  .mobile-overlay {
    display: none !important;
  }

  .app-container {
    grid-template-areas: "main";
    grid-template-columns: 1fr;
  }

  .main-content {
    background: white;
  }

  .content-wrapper {
    padding: 0;
  }
}

/* ===== ADDITIONAL DASHBOARD COMPONENTS ===== */
.dashboard-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--space-6);
  margin-top: var(--space-6);
}

.chart-card {
  background: white;
  padding: var(--space-6);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
}

.chart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-6);
}

.chart-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--gray-900);
}

.chart-placeholder {
  height: 300px;
  background: var(--gray-50);
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--gray-500);
  font-size: var(--font-size-sm);
}

.recent-orders {
  background: white;
  padding: var(--space-6);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
}

.orders-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-4);
}

.orders-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--gray-900);
}

.order-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-3) 0;
  border-bottom: 1px solid var(--gray-100);
}

.order-item:last-child {
  border-bottom: none;
}

.order-info {
  flex: 1;
}

.order-number {
  font-weight: 500;
  color: var(--gray-900);
  font-size: var(--font-size-sm);
}

.order-customer {
  color: var(--gray-600);
  font-size: var(--font-size-xs);
  margin-top: var(--space-1);
}

.order-status {
  padding: var(--space-1) var(--space-2);
  border-radius: var(--border-radius);
  font-size: var(--font-size-xs);
  font-weight: 500;
}

.order-status.pending {
  background: var(--warning-500);
  color: white;
}

.order-status.completed {
  background: var(--success-500);
  color: white;
}

.order-status.preparing {
  background: var(--primary-500);
  color: white;
}

@media (max-width: 1024px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
}

/* ===== LOADING STATES ===== */
.skeleton {
  background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.skeleton-text {
  height: 1rem;
  border-radius: var(--border-radius);
  margin-bottom: var(--space-2);
}

.skeleton-text.short {
  width: 60%;
}

.skeleton-text.medium {
  width: 80%;
}

.skeleton-text.long {
  width: 100%;
}
