// ===== LOCALIZATION DATA =====
const translations = {
  en: {
    title: "Restaurant POS Dashboard",
    loading: "Loading...",
    brand: "RestaurantPOS",
    search_placeholder: "Search orders, customers...",
    admin: "Admin",
    dashboard: "Dashboard",
    dashboard_subtitle: "Welcome back! Here's what's happening at your restaurant today.",
    orders: "Orders",
    new_order: "New Order",
    order_history: "Order History",
    pending_orders: "Pending Orders",
    menu: "Menu",
    menu_items: "Menu Items",
    categories: "Categories",
    modifiers: "Modifiers",
    customers: "Customers",
    reports: "Reports",
    sales_report: "Sales Report",
    inventory_report: "Inventory Report",
    staff_report: "Staff Report",
    settings: "Settings",
    today_sales: "Today's Sales",
    total_orders: "Total Orders",
    active_tables: "Active Tables",
    pending_orders_count: "Pending Orders"
  },
  ar: {
    title: "لوحة تحكم نظام نقاط البيع للمطاعم",
    loading: "جاري التحميل...",
    brand: "نظام المطعم",
    search_placeholder: "البحث في الطلبات والعملاء...",
    admin: "المدير",
    dashboard: "لوحة التحكم",
    dashboard_subtitle: "مرحباً بعودتك! إليك ما يحدث في مطعمك اليوم.",
    orders: "الطلبات",
    new_order: "طلب جديد",
    order_history: "تاريخ الطلبات",
    pending_orders: "الطلبات المعلقة",
    menu: "القائمة",
    menu_items: "عناصر القائمة",
    categories: "الفئات",
    modifiers: "المعدلات",
    customers: "العملاء",
    reports: "التقارير",
    sales_report: "تقرير المبيعات",
    inventory_report: "تقرير المخزون",
    staff_report: "تقرير الموظفين",
    settings: "الإعدادات",
    today_sales: "مبيعات اليوم",
    total_orders: "إجمالي الطلبات",
    active_tables: "الطاولات النشطة",
    pending_orders_count: "الطلبات المعلقة"
  }
};

// ===== APP STATE =====
class AppState {
  constructor() {
    this.currentLanguage = localStorage.getItem('language') || 'en';
    this.sidebarCollapsed = window.innerWidth < 768;
    this.sidebarVisible = false;
    this.activeSubmenu = null;
  }

  setLanguage(lang) {
    this.currentLanguage = lang;
    localStorage.setItem('language', lang);
    document.documentElement.lang = lang;
    document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';
  }

  toggleSidebar() {
    if (window.innerWidth < 768) {
      this.sidebarVisible = !this.sidebarVisible;
    } else {
      this.sidebarCollapsed = !this.sidebarCollapsed;
    }
  }

  toggleSubmenu(submenuId) {
    this.activeSubmenu = this.activeSubmenu === submenuId ? null : submenuId;
  }
}

// ===== PERFORMANCE UTILITIES =====
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

const throttle = (func, limit) => {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  }
};

// ===== LOCALIZATION SYSTEM =====
class LocalizationManager {
  constructor(appState) {
    this.appState = appState;
  }

  translate(key) {
    return translations[this.appState.currentLanguage][key] || key;
  }

  updateUI() {
    // Update all elements with data-i18n attribute
    document.querySelectorAll('[data-i18n]').forEach(element => {
      const key = element.getAttribute('data-i18n');
      element.textContent = this.translate(key);
    });

    // Update placeholder texts
    document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
      const key = element.getAttribute('data-i18n-placeholder');
      element.placeholder = this.translate(key);
    });

    // Update language button
    const langButton = document.getElementById('current-lang');
    if (langButton) {
      langButton.textContent = this.appState.currentLanguage.toUpperCase();
    }
  }

  switchLanguage(lang) {
    this.appState.setLanguage(lang);
    this.updateUI();
    this.hideLanguageDropdown();
  }

  showLanguageDropdown() {
    const dropdown = document.getElementById('lang-dropdown');
    if (dropdown) {
      dropdown.classList.add('show');
    }
  }

  hideLanguageDropdown() {
    const dropdown = document.getElementById('lang-dropdown');
    if (dropdown) {
      dropdown.classList.remove('show');
    }
  }
}

// ===== NAVIGATION MANAGER =====
class NavigationManager {
  constructor(appState) {
    this.appState = appState;
  }

  updateSidebarState() {
    const sidebar = document.getElementById('sidebar');
    const appContainer = document.querySelector('.app-container');
    const mobileOverlay = document.getElementById('mobile-overlay');

    if (window.innerWidth < 768) {
      // Mobile view
      if (this.appState.sidebarVisible) {
        sidebar.classList.add('show');
        mobileOverlay.classList.add('show');
      } else {
        sidebar.classList.remove('show');
        mobileOverlay.classList.remove('show');
      }
    } else {
      // Desktop view
      if (this.appState.sidebarCollapsed) {
        appContainer.classList.add('sidebar-collapsed');
      } else {
        appContainer.classList.remove('sidebar-collapsed');
      }
      sidebar.classList.remove('show');
      mobileOverlay.classList.remove('show');
    }
  }

  toggleSidebar() {
    this.appState.toggleSidebar();
    this.updateSidebarState();
  }

  closeSidebar() {
    if (window.innerWidth < 768) {
      this.appState.sidebarVisible = false;
      this.updateSidebarState();
    }
  }

  toggleSubmenu(submenuToggle) {
    const navItem = submenuToggle.closest('.nav-item');
    const submenuId = navItem.querySelector('.submenu')?.id || Math.random().toString(36);
    
    // Close other submenus
    document.querySelectorAll('.nav-item.expanded').forEach(item => {
      if (item !== navItem) {
        item.classList.remove('expanded');
      }
    });

    // Toggle current submenu
    navItem.classList.toggle('expanded');
    this.appState.toggleSubmenu(submenuId);
  }

  setActiveNavItem(link) {
    // Remove active class from all nav links
    document.querySelectorAll('.nav-link').forEach(navLink => {
      navLink.classList.remove('active');
    });
    
    // Add active class to clicked link
    link.classList.add('active');
  }
}

// ===== DASHBOARD MANAGER =====
class DashboardManager {
  constructor(localizationManager) {
    this.localizationManager = localizationManager;
  }

  renderDashboardContent() {
    const dashboardContent = document.getElementById('dashboard-content');
    if (!dashboardContent) return;

    const content = `
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-header">
            <span class="stat-title" data-i18n="today_sales">${this.localizationManager.translate('today_sales')}</span>
            <div class="stat-icon">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path d="M2 4h12M4 2v12M12 2v12" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
              </svg>
            </div>
          </div>
          <div class="stat-value">$2,847</div>
          <div class="stat-change positive">+12.5% from yesterday</div>
        </div>

        <div class="stat-card">
          <div class="stat-header">
            <span class="stat-title" data-i18n="total_orders">${this.localizationManager.translate('total_orders')}</span>
            <div class="stat-icon">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path d="M3 3h2l.4 2M7 13h6l2-8H5.4m1.6 8L5 3H2m5 10v2a1 1 0 0 0 1 1h4a1 1 0 0 0 1-1v-2" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <div class="stat-value">156</div>
          <div class="stat-change positive">+8.2% from yesterday</div>
        </div>

        <div class="stat-card">
          <div class="stat-header">
            <span class="stat-title" data-i18n="active_tables">${this.localizationManager.translate('active_tables')}</span>
            <div class="stat-icon">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                <rect x="2" y="6" width="12" height="8" rx="1" stroke="currentColor" stroke-width="1.5"/>
                <path d="M6 6V4a2 2 0 0 1 4 0v2" stroke="currentColor" stroke-width="1.5"/>
              </svg>
            </div>
          </div>
          <div class="stat-value">12/18</div>
          <div class="stat-change">67% occupancy</div>
        </div>

        <div class="stat-card">
          <div class="stat-header">
            <span class="stat-title" data-i18n="pending_orders_count">${this.localizationManager.translate('pending_orders_count')}</span>
            <div class="stat-icon">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                <circle cx="8" cy="8" r="6" stroke="currentColor" stroke-width="1.5"/>
                <path d="M8 4v4l2 2" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
              </svg>
            </div>
          </div>
          <div class="stat-value">7</div>
          <div class="stat-change negative">+2 from last hour</div>
        </div>
      </div>

      <div class="dashboard-grid">
        <div class="chart-card">
          <div class="chart-header">
            <h3 class="chart-title">Sales Overview</h3>
            <select class="btn btn-secondary">
              <option>Last 7 days</option>
              <option>Last 30 days</option>
              <option>Last 3 months</option>
            </select>
          </div>
          <div class="chart-placeholder">
            📊 Sales Chart Placeholder - Integrate with Chart.js or similar
          </div>
        </div>

        <div class="recent-orders">
          <div class="orders-header">
            <h3 class="orders-title">Recent Orders</h3>
            <a href="#orders" class="btn btn-secondary">View All</a>
          </div>
          <div class="orders-list">
            <div class="order-item">
              <div class="order-info">
                <div class="order-number">#ORD-001</div>
                <div class="order-customer">Table 5 - John Doe</div>
              </div>
              <div class="order-status preparing">Preparing</div>
            </div>
            <div class="order-item">
              <div class="order-info">
                <div class="order-number">#ORD-002</div>
                <div class="order-customer">Table 3 - Sarah Smith</div>
              </div>
              <div class="order-status pending">Pending</div>
            </div>
            <div class="order-item">
              <div class="order-info">
                <div class="order-number">#ORD-003</div>
                <div class="order-customer">Takeaway - Mike Johnson</div>
              </div>
              <div class="order-status completed">Completed</div>
            </div>
            <div class="order-item">
              <div class="order-info">
                <div class="order-number">#ORD-004</div>
                <div class="order-customer">Table 8 - Emma Wilson</div>
              </div>
              <div class="order-status preparing">Preparing</div>
            </div>
            <div class="order-item">
              <div class="order-info">
                <div class="order-number">#ORD-005</div>
                <div class="order-customer">Delivery - Alex Brown</div>
              </div>
              <div class="order-status pending">Pending</div>
            </div>
          </div>
        </div>
      </div>
    `;

    dashboardContent.innerHTML = content;
  }
}

// ===== MAIN APPLICATION =====
class RestaurantPOSApp {
  constructor() {
    this.appState = new AppState();
    this.localizationManager = new LocalizationManager(this.appState);
    this.navigationManager = new NavigationManager(this.appState);
    this.dashboardManager = new DashboardManager(this.localizationManager);

    this.init();
  }

  init() {
    // Set initial language and direction
    this.localizationManager.appState.setLanguage(this.appState.currentLanguage);

    // Initialize UI after DOM is loaded
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.initializeUI());
    } else {
      this.initializeUI();
    }
  }

  initializeUI() {
    // Update localization
    this.localizationManager.updateUI();

    // Initialize navigation state
    this.navigationManager.updateSidebarState();

    // Render dashboard content
    this.dashboardManager.renderDashboardContent();

    // Setup event listeners
    this.setupEventListeners();

    // Hide loading screen
    this.hideLoadingScreen();
  }

  setupEventListeners() {
    // Sidebar toggle
    const sidebarToggle = document.getElementById('sidebar-toggle');
    if (sidebarToggle) {
      sidebarToggle.addEventListener('click', () => {
        this.navigationManager.toggleSidebar();
      });
    }

    // Mobile overlay click
    const mobileOverlay = document.getElementById('mobile-overlay');
    if (mobileOverlay) {
      mobileOverlay.addEventListener('click', () => {
        this.navigationManager.closeSidebar();
      });
    }

    // Language switcher
    const langToggle = document.getElementById('lang-toggle');
    const langDropdown = document.getElementById('lang-dropdown');

    if (langToggle && langDropdown) {
      langToggle.addEventListener('click', (e) => {
        e.stopPropagation();
        this.localizationManager.showLanguageDropdown();
      });

      // Language options
      langDropdown.querySelectorAll('.lang-option').forEach(option => {
        option.addEventListener('click', (e) => {
          const lang = e.target.getAttribute('data-lang');
          this.localizationManager.switchLanguage(lang);
        });
      });
    }

    // Close dropdowns when clicking outside
    document.addEventListener('click', () => {
      this.localizationManager.hideLanguageDropdown();
    });

    // Submenu toggles
    document.querySelectorAll('.submenu-toggle').forEach(toggle => {
      toggle.addEventListener('click', (e) => {
        e.preventDefault();
        this.navigationManager.toggleSubmenu(toggle);
      });
    });

    // Navigation links
    document.querySelectorAll('.nav-link:not(.submenu-toggle)').forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        this.navigationManager.setActiveNavItem(link);

        // Close sidebar on mobile after navigation
        if (window.innerWidth < 768) {
          this.navigationManager.closeSidebar();
        }
      });
    });

    // Submenu links
    document.querySelectorAll('.submenu-link').forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();

        // Close sidebar on mobile after navigation
        if (window.innerWidth < 768) {
          this.navigationManager.closeSidebar();
        }
      });
    });

    // Window resize handler
    const handleResize = throttle(() => {
      const wasDesktop = !this.appState.sidebarCollapsed && window.innerWidth >= 768;
      const isMobile = window.innerWidth < 768;

      if (isMobile && wasDesktop) {
        this.appState.sidebarVisible = false;
      } else if (!isMobile && this.appState.sidebarVisible) {
        this.appState.sidebarVisible = false;
        this.appState.sidebarCollapsed = false;
      }

      this.navigationManager.updateSidebarState();
    }, 250);

    window.addEventListener('resize', handleResize);

    // Search functionality
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
      const handleSearch = debounce((query) => {
        console.log('Searching for:', query);
        // Implement search functionality here
      }, 300);

      searchInput.addEventListener('input', (e) => {
        handleSearch(e.target.value);
      });
    }

    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
      // ESC key closes sidebar and dropdowns
      if (e.key === 'Escape') {
        this.navigationManager.closeSidebar();
        this.localizationManager.hideLanguageDropdown();
      }

      // Alt + M toggles sidebar
      if (e.altKey && e.key === 'm') {
        e.preventDefault();
        this.navigationManager.toggleSidebar();
      }

      // Alt + L toggles language
      if (e.altKey && e.key === 'l') {
        e.preventDefault();
        const currentLang = this.appState.currentLanguage;
        const newLang = currentLang === 'en' ? 'ar' : 'en';
        this.localizationManager.switchLanguage(newLang);
      }
    });
  }

  hideLoadingScreen() {
    const loadingScreen = document.getElementById('loading-screen');
    if (loadingScreen) {
      setTimeout(() => {
        loadingScreen.classList.add('hidden');
        setTimeout(() => {
          loadingScreen.style.display = 'none';
        }, 250);
      }, 500);
    }
  }
}

// ===== PERFORMANCE OPTIMIZATIONS =====
// Lazy load images
const lazyLoadImages = () => {
  const images = document.querySelectorAll('img[data-src]');
  const imageObserver = new IntersectionObserver((entries, observer) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target;
        img.src = img.dataset.src;
        img.removeAttribute('data-src');
        observer.unobserve(img);
      }
    });
  });

  images.forEach(img => imageObserver.observe(img));
};

// Preload critical resources
const preloadCriticalResources = () => {
  const criticalFonts = [
    'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap',
    'https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap'
  ];

  criticalFonts.forEach(font => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'style';
    link.href = font;
    document.head.appendChild(link);
  });
};

// ===== APPLICATION INITIALIZATION =====
// Initialize the application
const app = new RestaurantPOSApp();

// Initialize performance optimizations
document.addEventListener('DOMContentLoaded', () => {
  lazyLoadImages();
  preloadCriticalResources();
});

// Service Worker registration for PWA capabilities (optional)
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then(registration => {
        console.log('SW registered: ', registration);
      })
      .catch(registrationError => {
        console.log('SW registration failed: ', registrationError);
      });
  });
}
