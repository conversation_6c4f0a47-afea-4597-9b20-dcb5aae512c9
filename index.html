<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-i18n="title">Restaurant POS Dashboard</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="manifest" href="manifest.json">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <meta name="theme-color" content="#3b82f6">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="RestaurantPOS">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-spinner"></div>
        <p data-i18n="loading">Loading...</p>
    </div>

    <!-- Main App Container -->
    <div id="app" class="app-container">
        <!-- Top Navigation Bar -->
        <nav class="navbar" role="navigation" aria-label="Main navigation">
            <div class="navbar-content">
                <!-- Logo and Brand -->
                <div class="navbar-brand">
                    <button class="sidebar-toggle" id="sidebar-toggle" aria-label="Toggle sidebar">
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                    </button>
                    <div class="logo">
                        <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                            <rect width="32" height="32" rx="8" fill="currentColor"/>
                            <path d="M8 12h16M8 16h16M8 20h12" stroke="white" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                        <span class="logo-text" data-i18n="brand">RestaurantPOS</span>
                    </div>
                </div>

                <!-- Search Bar -->
                <div class="navbar-search">
                    <div class="search-container">
                        <svg class="search-icon" width="20" height="20" viewBox="0 0 20 20" fill="none">
                            <path d="M17.5 17.5L12.5 12.5M14.1667 8.33333C14.1667 11.555 11.555 14.1667 8.33333 14.1667C5.11167 14.1667 2.5 11.555 2.5 8.33333C2.5 5.11167 5.11167 2.5 8.33333 2.5C11.555 2.5 14.1667 5.11167 14.1667 8.33333Z" stroke="currentColor" stroke-width="1.67" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <input type="text" class="search-input" data-i18n-placeholder="search_placeholder" placeholder="Search orders, customers...">
                    </div>
                </div>

                <!-- Right Side Controls -->
                <div class="navbar-controls">
                    <!-- Language Switcher -->
                    <div class="language-switcher">
                        <button class="lang-btn" id="lang-toggle" aria-label="Switch language">
                            <span class="lang-text" id="current-lang">EN</span>
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                <path d="M4 6L8 10L12 6" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </button>
                        <div class="lang-dropdown" id="lang-dropdown">
                            <button class="lang-option" data-lang="en">English</button>
                            <button class="lang-option" data-lang="ar">العربية</button>
                        </div>
                    </div>

                    <!-- Notifications -->
                    <button class="notification-btn" aria-label="Notifications">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9M13.73 21a2 2 0 0 1-3.46 0" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <span class="notification-badge">3</span>
                    </button>

                    <!-- User Profile -->
                    <div class="user-profile">
                        <button class="profile-btn" id="profile-toggle">
                            <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 32 32'%3E%3Ccircle cx='16' cy='16' r='16' fill='%23e5e7eb'/%3E%3Cpath d='M16 16a4 4 0 1 0 0-8 4 4 0 0 0 0 8zM8 24a8 8 0 0 1 16 0' fill='%236b7280'/%3E%3C/svg%3E" alt="User avatar" class="profile-avatar">
                            <span class="profile-name" data-i18n="admin">Admin</span>
                        </button>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar" role="navigation" aria-label="Sidebar navigation">
            <div class="sidebar-content">
                <nav class="sidebar-nav">
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="#dashboard" class="nav-link active" data-i18n="dashboard">
                                <svg class="nav-icon" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                    <path d="M3 4a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V4zM3 10a1 1 0 0 1 1-1h6a1 1 0 0 1 1 1v6a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1v-6zM14 9a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1v-6a1 1 0 0 0-1-1h-2z" fill="currentColor"/>
                                </svg>
                                Dashboard
                            </a>
                        </li>
                        
                        <li class="nav-item has-submenu">
                            <button class="nav-link submenu-toggle" data-i18n="orders">
                                <svg class="nav-icon" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                    <path d="M3 3h2l.4 2M7 13h10l4-8H5.4m1.6 8L5 3H2m5 10v6a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1v-6m-9 0h8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                Orders
                                <svg class="submenu-arrow" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                    <path d="M6 4L10 8L6 12" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </button>
                            <ul class="submenu">
                                <li><a href="#new-order" class="submenu-link" data-i18n="new_order">New Order</a></li>
                                <li><a href="#order-history" class="submenu-link" data-i18n="order_history">Order History</a></li>
                                <li><a href="#pending-orders" class="submenu-link" data-i18n="pending_orders">Pending Orders</a></li>
                            </ul>
                        </li>

                        <li class="nav-item has-submenu">
                            <button class="nav-link submenu-toggle" data-i18n="menu">
                                <svg class="nav-icon" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                    <path d="M4 6h16M4 12h16M4 18h16" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                </svg>
                                Menu
                                <svg class="submenu-arrow" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                    <path d="M6 4L10 8L6 12" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </button>
                            <ul class="submenu">
                                <li><a href="#menu-items" class="submenu-link" data-i18n="menu_items">Menu Items</a></li>
                                <li><a href="#categories" class="submenu-link" data-i18n="categories">Categories</a></li>
                                <li><a href="#modifiers" class="submenu-link" data-i18n="modifiers">Modifiers</a></li>
                            </ul>
                        </li>

                        <li class="nav-item">
                            <a href="#customers" class="nav-link" data-i18n="customers">
                                <svg class="nav-icon" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                    <path d="M13 6a3 3 0 1 1-6 0 3 3 0 0 1 6 0zM18 8a2 2 0 1 1-4 0 2 2 0 0 1 4 0zM14 15a4 4 0 0 0-8 0v3h8v-3z" fill="currentColor"/>
                                </svg>
                                Customers
                            </a>
                        </li>

                        <li class="nav-item has-submenu">
                            <button class="nav-link submenu-toggle" data-i18n="reports">
                                <svg class="nav-icon" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                    <path d="M9 17V7m0 10a2 2 0 0 1-2-2V9a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v6a2 2 0 0 1-2 2H9z" fill="currentColor"/>
                                </svg>
                                Reports
                                <svg class="submenu-arrow" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                    <path d="M6 4L10 8L6 12" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </button>
                            <ul class="submenu">
                                <li><a href="#sales-report" class="submenu-link" data-i18n="sales_report">Sales Report</a></li>
                                <li><a href="#inventory-report" class="submenu-link" data-i18n="inventory_report">Inventory Report</a></li>
                                <li><a href="#staff-report" class="submenu-link" data-i18n="staff_report">Staff Report</a></li>
                            </ul>
                        </li>

                        <li class="nav-item">
                            <a href="#settings" class="nav-link" data-i18n="settings">
                                <svg class="nav-icon" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                    <path d="M10 6a4 4 0 1 0 0 8 4 4 0 0 0 0-8zM2.458 12C3.732 16.657 5.943 18.868 10.6 20.142L12 18.6c1.542-.4 2.6-1.458 3-3L18.6 12c1.274-4.657-.937-6.868-5.594-8.142L12 5.4c-1.542.4-2.6 1.458-3 3L5.4 12z" stroke="currentColor" stroke-width="1.5"/>
                                </svg>
                                Settings
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </aside>

        <!-- Main Content Area -->
        <main class="main-content" id="main-content">
            <div class="content-wrapper">
                <!-- Page Header -->
                <header class="page-header">
                    <div class="page-title">
                        <h1 data-i18n="dashboard">Dashboard</h1>
                        <p class="page-subtitle" data-i18n="dashboard_subtitle">Welcome back! Here's what's happening at your restaurant today.</p>
                    </div>
                    <div class="page-actions">
                        <button class="btn btn-primary" data-i18n="new_order">
                            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                                <path d="M10 4v12M4 10h12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                            </svg>
                            New Order
                        </button>
                    </div>
                </header>

                <!-- Dashboard Content -->
                <div class="dashboard-content" id="dashboard-content">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </main>
    </div>

    <!-- Mobile Overlay -->
    <div class="mobile-overlay" id="mobile-overlay"></div>

    <script src="script.js"></script>
</body>
</html>
