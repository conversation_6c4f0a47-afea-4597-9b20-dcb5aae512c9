{"name": "Restaurant POS Dashboard", "short_name": "RestaurantPOS", "description": "Modern restaurant point of sale dashboard with multilingual support", "start_url": "/", "display": "standalone", "background_color": "#ffffff", "theme_color": "#3b82f6", "orientation": "portrait-primary", "scope": "/", "lang": "en", "dir": "auto", "categories": ["business", "productivity", "food"], "icons": [{"src": "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='192' height='192' viewBox='0 0 192 192'%3E%3Crect width='192' height='192' rx='32' fill='%233b82f6'/%3E%3Cpath d='M48 72h96M48 96h96M48 120h72' stroke='white' stroke-width='8' stroke-linecap='round'/%3E%3C/svg%3E", "sizes": "192x192", "type": "image/svg+xml", "purpose": "any maskable"}, {"src": "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='512' height='512' viewBox='0 0 512 512'%3E%3Crect width='512' height='512' rx='85' fill='%233b82f6'/%3E%3Cpath d='M128 192h256M128 256h256M128 320h192' stroke='white' stroke-width='20' stroke-linecap='round'/%3E%3C/svg%3E", "sizes": "512x512", "type": "image/svg+xml", "purpose": "any maskable"}], "screenshots": [{"src": "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='1280' height='720' viewBox='0 0 1280 720'%3E%3Crect width='1280' height='720' fill='%23f9fafb'/%3E%3Crect x='0' y='0' width='1280' height='64' fill='white'/%3E%3Crect x='0' y='64' width='256' height='656' fill='white'/%3E%3Ctext x='640' y='360' text-anchor='middle' font-family='Arial' font-size='24' fill='%236b7280'%3ERestaurant POS Dashboard%3C/text%3E%3C/svg%3E", "sizes": "1280x720", "type": "image/svg+xml", "form_factor": "wide", "label": "Desktop Dashboard View"}, {"src": "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='390' height='844' viewBox='0 0 390 844'%3E%3Crect width='390' height='844' fill='%23f9fafb'/%3E%3Crect x='0' y='0' width='390' height='56' fill='white'/%3E%3Ctext x='195' y='422' text-anchor='middle' font-family='Arial' font-size='16' fill='%236b7280'%3EMobile Dashboard%3C/text%3E%3C/svg%3E", "sizes": "390x844", "type": "image/svg+xml", "form_factor": "narrow", "label": "Mobile Dashboard View"}], "shortcuts": [{"name": "New Order", "short_name": "New Order", "description": "Create a new order", "url": "/#new-order", "icons": [{"src": "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='96' height='96' viewBox='0 0 96 96'%3E%3Crect width='96' height='96' rx='16' fill='%2310b981'/%3E%3Cpath d='M48 24v48M24 48h48' stroke='white' stroke-width='4' stroke-linecap='round'/%3E%3C/svg%3E", "sizes": "96x96", "type": "image/svg+xml"}]}, {"name": "Orders", "short_name": "Orders", "description": "View all orders", "url": "/#orders", "icons": [{"src": "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='96' height='96' viewBox='0 0 96 96'%3E%3Crect width='96' height='96' rx='16' fill='%23f59e0b'/%3E%3Cpath d='M18 18h12l2.4 12M42 78h36l12-48H32.4m9.6 48L30 18H12m30 60v12a6 6 0 0 0 6 6h24a6 6 0 0 0 6-6V78' stroke='white' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E", "sizes": "96x96", "type": "image/svg+xml"}]}, {"name": "<PERSON><PERSON>", "short_name": "<PERSON><PERSON>", "description": "Manage menu items", "url": "/#menu", "icons": [{"src": "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='96' height='96' viewBox='0 0 96 96'%3E%3Crect width='96' height='96' rx='16' fill='%236366f1'/%3E%3Cpath d='M24 36h48M24 48h48M24 60h48' stroke='white' stroke-width='4' stroke-linecap='round'/%3E%3C/svg%3E", "sizes": "96x96", "type": "image/svg+xml"}]}, {"name": "Reports", "short_name": "Reports", "description": "View sales reports", "url": "/#reports", "icons": [{"src": "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='96' height='96' viewBox='0 0 96 96'%3E%3Crect width='96' height='96' rx='16' fill='%23ec4899'/%3E%3Cpath d='M54 102V42m0 60a12 12 0 0 1-12-12V54a12 12 0 0 1 12-12h12a12 12 0 0 1 12 12v36a12 12 0 0 1-12 12H54z' fill='white'/%3E%3C/svg%3E", "sizes": "96x96", "type": "image/svg+xml"}]}], "related_applications": [], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}}